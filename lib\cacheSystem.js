import NodeCache from 'node-cache';
import logger from './logger.js';

// ============================================================================
// SISTEMA DE CACHÉ MULTICAPA - Optimizado para 50+ usuarios simultáneos
// ============================================================================

class CacheSystem {
  constructor() {
    // L1: Caché en memoria (más rápido, por instancia)
    this.memoryCache = new NodeCache({
      stdTTL: 300, // 5 minutos por defecto
      checkperiod: 60, // Verificar expiración cada minuto
      maxKeys: 2000, // Máximo 2000 keys en memoria
      useClones: false, // Mejor performance, pero cuidado con mutaciones
      deleteOnExpire: true
    });

    // L2: Caché distribuido (Redis si está disponible)
    this.distributedCache = null;
    this.initializeDistributedCache();

    // Configuración de TTL por tipo de contenido
    this.ttlConfig = {
      'products': 600, // 10 minutos
      'catalog': 300, // 5 minutos
      'categories': 1800, // 30 minutos
      'health': 60, // 1 minuto
      'search': 180, // 3 minutos
      'user_profile': 900, // 15 minutos
      'static_content': 3600 // 1 hora
    };

    // Métricas de caché
    this.metrics = {
      l1Hits: 0,
      l1Misses: 0,
      l2Hits: 0,
      l2Misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      avgResponseTime: 0
    };

    // Configurar eventos
    this.setupEventHandlers();
    
    logger.info('Cache system initialized', {
      memoryCache: true,
      distributedCache: !!this.distributedCache,
      maxKeys: 2000
    });
  }

  async initializeDistributedCache() {
    try {
      // Intentar conectar a Redis si está configurado
      if (process.env.REDIS_URL) {
        const Redis = await import('ioredis');
        this.distributedCache = new Redis.default(process.env.REDIS_URL, {
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
          lazyConnect: true,
          keepAlive: 30000,
          connectTimeout: 10000,
          commandTimeout: 5000
        });

        // Verificar conexión
        await this.distributedCache.ping();
        logger.info('Redis cache connected successfully');
        
        // Configurar eventos de Redis
        this.distributedCache.on('error', (error) => {
          logger.error('Redis cache error', { error: error.message });
          this.metrics.errors++;
        });

        this.distributedCache.on('reconnecting', () => {
          logger.warn('Redis cache reconnecting...');
        });

      }
    } catch (error) {
      logger.warn('Distributed cache not available, using memory cache only', { 
        error: error.message 
      });
      this.distributedCache = null;
    }
  }

  setupEventHandlers() {
    // Eventos del caché en memoria
    this.memoryCache.on('set', (key, value) => {
      logger.debug('Memory cache set', { key, size: JSON.stringify(value).length });
    });

    this.memoryCache.on('del', (key, value) => {
      logger.debug('Memory cache delete', { key });
    });

    this.memoryCache.on('expired', (key, value) => {
      logger.debug('Memory cache expired', { key });
    });
  }

  // Generar clave de caché con namespace
  generateKey(namespace, key, params = {}) {
    const paramString = Object.keys(params).length > 0 
      ? ':' + JSON.stringify(params) 
      : '';
    return `${namespace}:${key}${paramString}`;
  }

  // Obtener TTL para un namespace
  getTTL(namespace) {
    return this.ttlConfig[namespace] || 300; // 5 minutos por defecto
  }

  async get(namespace, key, params = {}) {
    const startTime = Date.now();
    const cacheKey = this.generateKey(namespace, key, params);

    try {
      // L1: Intentar caché en memoria primero
      let value = this.memoryCache.get(cacheKey);
      if (value !== undefined) {
        this.metrics.l1Hits++;
        this.updateAvgResponseTime(Date.now() - startTime);
        
        logger.debug('L1 cache hit', { key: cacheKey });
        return value;
      }
      this.metrics.l1Misses++;

      // L2: Intentar caché distribuido
      if (this.distributedCache) {
        try {
          const redisValue = await this.distributedCache.get(cacheKey);
          if (redisValue !== null) {
            value = JSON.parse(redisValue);
            
            // Poblar L1 para próximas consultas
            const ttl = this.getTTL(namespace);
            this.memoryCache.set(cacheKey, value, ttl);
            
            this.metrics.l2Hits++;
            this.updateAvgResponseTime(Date.now() - startTime);
            
            logger.debug('L2 cache hit', { key: cacheKey });
            return value;
          }
        } catch (redisError) {
          logger.warn('Redis get error', { 
            key: cacheKey, 
            error: redisError.message 
          });
          this.metrics.errors++;
        }
      }
      this.metrics.l2Misses++;

      this.updateAvgResponseTime(Date.now() - startTime);
      return null;

    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache get error', { 
        key: cacheKey, 
        error: error.message 
      });
      return null;
    }
  }

  async set(namespace, key, value, customTTL = null, params = {}) {
    const cacheKey = this.generateKey(namespace, key, params);
    const ttl = customTTL || this.getTTL(namespace);

    try {
      // Validar tamaño del valor
      const valueSize = JSON.stringify(value).length;
      if (valueSize > 1024 * 1024) { // 1MB límite
        logger.warn('Cache value too large, skipping', { 
          key: cacheKey, 
          size: valueSize 
        });
        return false;
      }

      // L1: Guardar en memoria
      this.memoryCache.set(cacheKey, value, ttl);

      // L2: Guardar en caché distribuido
      if (this.distributedCache) {
        try {
          await this.distributedCache.setex(
            cacheKey, 
            ttl, 
            JSON.stringify(value)
          );
        } catch (redisError) {
          logger.warn('Redis set error', { 
            key: cacheKey, 
            error: redisError.message 
          });
          this.metrics.errors++;
        }
      }

      this.metrics.sets++;
      logger.debug('Cache set', { key: cacheKey, ttl, size: valueSize });
      return true;

    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache set error', { 
        key: cacheKey, 
        error: error.message 
      });
      return false;
    }
  }

  async delete(namespace, key, params = {}) {
    const cacheKey = this.generateKey(namespace, key, params);

    try {
      // L1: Eliminar de memoria
      this.memoryCache.del(cacheKey);

      // L2: Eliminar de caché distribuido
      if (this.distributedCache) {
        try {
          await this.distributedCache.del(cacheKey);
        } catch (redisError) {
          logger.warn('Redis delete error', { 
            key: cacheKey, 
            error: redisError.message 
          });
          this.metrics.errors++;
        }
      }

      this.metrics.deletes++;
      logger.debug('Cache delete', { key: cacheKey });
      return true;

    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache delete error', { 
        key: cacheKey, 
        error: error.message 
      });
      return false;
    }
  }

  async invalidateNamespace(namespace) {
    try {
      // L1: Buscar y eliminar keys con el namespace
      const keys = this.memoryCache.keys();
      const namespaceKeys = keys.filter(key => key.startsWith(`${namespace}:`));
      
      for (const key of namespaceKeys) {
        this.memoryCache.del(key);
      }

      // L2: Invalidar en Redis usando patrón
      if (this.distributedCache) {
        try {
          const redisKeys = await this.distributedCache.keys(`${namespace}:*`);
          if (redisKeys.length > 0) {
            await this.distributedCache.del(...redisKeys);
          }
        } catch (redisError) {
          logger.warn('Redis namespace invalidation error', { 
            namespace, 
            error: redisError.message 
          });
          this.metrics.errors++;
        }
      }

      logger.info('Cache namespace invalidated', { 
        namespace, 
        keysInvalidated: namespaceKeys.length 
      });
      return true;

    } catch (error) {
      this.metrics.errors++;
      logger.error('Cache namespace invalidation error', { 
        namespace, 
        error: error.message 
      });
      return false;
    }
  }

  // Wrapper para funciones que necesitan caché
  async wrap(namespace, key, fetchFunction, customTTL = null, params = {}) {
    // Intentar obtener del caché
    let value = await this.get(namespace, key, params);
    
    if (value !== null) {
      return value;
    }

    // No está en caché, ejecutar función
    try {
      value = await fetchFunction();
      
      // Guardar en caché si el valor es válido
      if (value !== null && value !== undefined) {
        await this.set(namespace, key, value, customTTL, params);
      }
      
      return value;
    } catch (error) {
      logger.error('Cache wrap function error', { 
        namespace, 
        key, 
        error: error.message 
      });
      throw error;
    }
  }

  updateAvgResponseTime(responseTime) {
    this.metrics.avgResponseTime = 
      (this.metrics.avgResponseTime * 0.9) + (responseTime * 0.1);
  }

  getMetrics() {
    const memoryStats = this.memoryCache.getStats();
    
    return {
      ...this.metrics,
      l1Stats: {
        keys: memoryStats.keys,
        hits: memoryStats.hits,
        misses: memoryStats.misses,
        ksize: memoryStats.ksize,
        vsize: memoryStats.vsize
      },
      l2Available: !!this.distributedCache,
      hitRate: {
        l1: this.metrics.l1Hits / (this.metrics.l1Hits + this.metrics.l1Misses) || 0,
        l2: this.metrics.l2Hits / (this.metrics.l2Hits + this.metrics.l2Misses) || 0,
        overall: (this.metrics.l1Hits + this.metrics.l2Hits) / 
                (this.metrics.l1Hits + this.metrics.l1Misses + this.metrics.l2Hits + this.metrics.l2Misses) || 0
      }
    };
  }

  async healthCheck() {
    const health = {
      l1: true,
      l2: false,
      errors: []
    };

    try {
      // Test L1
      const testKey = 'health_check_' + Date.now();
      this.memoryCache.set(testKey, 'test', 10);
      const testValue = this.memoryCache.get(testKey);
      this.memoryCache.del(testKey);
      
      if (testValue !== 'test') {
        health.l1 = false;
        health.errors.push('L1 cache test failed');
      }
    } catch (error) {
      health.l1 = false;
      health.errors.push(`L1 cache error: ${error.message}`);
    }

    // Test L2
    if (this.distributedCache) {
      try {
        await this.distributedCache.ping();
        health.l2 = true;
      } catch (error) {
        health.l2 = false;
        health.errors.push(`L2 cache error: ${error.message}`);
      }
    }

    return health;
  }

  async shutdown() {
    logger.info('Shutting down cache system...');
    
    // Cerrar conexión Redis
    if (this.distributedCache) {
      try {
        await this.distributedCache.quit();
      } catch (error) {
        logger.error('Error closing Redis connection', { error: error.message });
      }
    }
    
    // Limpiar caché en memoria
    this.memoryCache.flushAll();
    
    logger.info('Cache system shutdown complete');
  }
}

// Singleton instance
export const cacheSystem = new CacheSystem();

// Graceful shutdown
process.on('SIGTERM', async () => {
  await cacheSystem.shutdown();
});

process.on('SIGINT', async () => {
  await cacheSystem.shutdown();
});

export default cacheSystem;
