import React, { useState } from 'react';
import { ShoppingCart, Tag, Info } from 'lucide-react';
import type { CatalogProduct } from '../types/catalog'; // Importar CatalogProduct
import { Link, useNavigate } from 'react-router-dom';
import { IMAGES } from '../config/constants';
import { useAuth } from '../context/AuthContext';
import { STORAGE_CONFIG, getPublicImageUrl } from '../config/storage';
import { ProductImage } from './ProductImage';
import { toast } from 'react-toastify';
import { FEATURE_FLAGS } from '../config/featureFlags';
import ProductTags from './ProductTags';

interface ProductCardProps {
    product: CatalogProduct; // Cambiar a CatalogProduct
    onAddToCart: (product: CatalogProduct, quantity: number) => void; // Cambiar a CatalogProduct
    showAddToCart: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
    product,
    onAddToCart,
    showAddToCart
}) => {
  const { isAuthenticated } = useAuth();
  const [quantity, setQuantity] = useState(1);
  const navigate = useNavigate();
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  // Desestructurar campos de CatalogProduct.
  // 'category' aquí se refiere a la categoría principal para visualización.
  // 'image_url' es el campo principal para la imagen. 'imageUrl' (lowercase L) también está disponible si se necesita.
  const { id, name, description, price, category, stock, image_url, brand } = product;
  const [imgSrc, setImgSrc] = useState<string>(() => {
    if (!image_url) return IMAGES.DEFAULT_PRODUCT; // Usar image_url
    
    if (image_url.startsWith('http')) { // Usar image_url
      return image_url;
    }
    
    try {
      return getPublicImageUrl(image_url); // Usar image_url
    } catch {
      return IMAGES.DEFAULT_PRODUCT;
    }
  });
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);

    const handleImageError = () => {
        console.warn(`Failed to load image for product ${product.id}`);
        setImgSrc(IMAGES.DEFAULT_PRODUCT);
    };

    const handleImageLoad = () => {
        setIsLoading(false);
    };

    const handleQuantityChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const newQuantity = parseInt(event.target.value, 10);
        setQuantity(isNaN(newQuantity) || newQuantity < 1 ? 1 : newQuantity);
    };

    const handleCardClick = (e: React.MouseEvent) => {
        if (
            !(e.target as HTMLElement).closest('input') && 
            !(e.target as HTMLElement).closest('button')
        ) {
            navigate(`/product/${id}`);
        }
    };

    const handleMouseMove = (e: React.MouseEvent) => {
        setTooltipPosition({
            x: e.clientX + 10,
            y: e.clientY + 10
        });
    };

    return (
        <div
            onClick={handleCardClick}
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}
            onMouseMove={handleMouseMove}
            className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 cursor-pointer flex flex-col h-full relative max-w-[280px] mx-auto w-full"
        >
            {/* Tooltip */}
            {showTooltip && product.description && (
                <div 
                    className="fixed z-50 bg-gray-900 text-white p-2 rounded-md text-sm max-w-xs"
                    style={{
                        left: tooltipPosition.x,
                        top: tooltipPosition.y,
                        pointerEvents: 'none'
                    }}
                >
                    {product.description}
                </div>
            )}

            {/* Imagen con aspect ratio fijo */}
            <Link to={`/product/${id}`} className="block aspect-square overflow-hidden rounded-t-lg">
                <div className="relative">
                    {isLoading && (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                            <div className="animate-pulse w-full h-full bg-gray-200" />
                        </div>
                    )}
                    <img
                        src={imgSrc}
                        alt={name}
                        className={`w-full h-full object-cover transition-opacity duration-300 ${
                            isLoading ? 'opacity-0' : 'opacity-100'
                        }`}
                        onError={handleImageError}
                        onLoad={handleImageLoad}
                        loading="lazy"
                    />
                    {/* Tag de la marca */}
                    {brand && (
                        <div className="absolute top-2 right-2 bg-black/60 text-white text-xs font-semibold px-2.5 py-1 rounded-full backdrop-blur-sm">
                            {brand}
                        </div>
                    )}
                </div>
            </Link>
            
            <div className="p-4 flex flex-col flex-grow">
                {/* Contenido */}
                <div className="flex-grow">
                    <div className="text-sm text-gray-500 mb-2">{category}</div> {/* 'category' ya está desestructurado */}
                    <h3 className="text-base font-medium text-gray-900 mb-2 line-clamp-2">
                      {name}
                    </h3>

                    {/* Tags del producto */}
                    {product.tags && product.tags.length > 0 && (
                      <div className="mb-3">
                        <ProductTags
                          tags={product.tags}
                          maxVisible={3}
                          size="sm"
                          variant="filled"
                          interactive={false}
                        />
                      </div>
                    )}

                    {/* Propiedades destacadas */}
                    {product.properties && Object.keys(product.properties).length > 0 && (
                      <div className="mb-3">
                        <div className="flex items-center gap-1 text-xs text-gray-600 mb-1">
                          <Info className="w-3 h-3" />
                          <span>Características</span>
                        </div>
                        <div className="text-xs text-gray-700 space-y-1">
                          {Object.entries(product.properties).slice(0, 2).map(([key, value]) => (
                            <div key={key} className="flex justify-between">
                              <span className="capitalize">{key.replace(/_/g, ' ')}:</span>
                              <span className="font-medium">
                                {Array.isArray(value) ? value.join(', ') : String(value)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {isAuthenticated && (
                      <p className="text-xl font-bold text-blue-600">
                        ${Number(price).toFixed(2)} {/* 'price' ya está desestructurado */}
                      </p>
                    )}
                  </div>

                {/* Controles siempre al fondo */}
                <div className="flex items-center gap-2 pt-4 mt-auto border-t border-gray-100">
                    <input
                        type="number"
                        min="1"
                        value={quantity}
                        onChange={(e) => setQuantity(Number(e.target.value))}
                        className="w-20 px-3 py-2 text-sm border rounded-md focus:ring-2 focus:ring-blue-500"
                        onClick={(e) => e.stopPropagation()}
                    />
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            if (FEATURE_FLAGS.ALLOW_GUEST_CART || isAuthenticated) {
                                onAddToCart(product, quantity);
                                toast.success('Producto añadido al pedido');
                            } else {
                                toast.error('Debes iniciar sesión para agregar productos al carrito');
                            }
                        }}
                        className="flex-1 flex items-center justify-center gap-2 bg-blue-600 text-white 
                                 px-4 py-2 rounded-md hover:bg-blue-700 text-sm font-medium transition-colors"
                    >
                        <ShoppingCart className="w-4 h-4" />
                        <span>{isAuthenticated ? 'Agregar' : 'Cotizar'}</span>
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ProductCard;
