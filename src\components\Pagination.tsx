import React, { useMemo } from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  itemsPerPage: number;
  itemsPerPageOptions: number[];
  onItemsPerPageChange: (itemsPerPage: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  itemsPerPage,
  itemsPerPageOptions,
  onItemsPerPageChange
}) => {
  const pageNumbers = useMemo(() => {
    const range = (start: number, end: number) => {
      const length = end - start + 1;
      return Array.from({ length }, (_, idx) => idx + start);
    };

    // Si el total de páginas es 7 o menos, muéstralas todas.
    if (totalPages <= 7) {
      return range(1, totalPages);
    }

    // Si la página actual está cerca del inicio.
    if (currentPage <= 4) {
      return [...range(1, 5), '...', totalPages];
    }

    // Si la página actual está cerca del final.
    if (currentPage >= totalPages - 3) {
      return [1, '...', ...range(totalPages - 4, totalPages)];
    }

    // Si la página actual está en el medio.
    return [1, '...', ...range(currentPage - 2, currentPage + 2), '...', totalPages];
  }, [currentPage, totalPages]);

  if (totalPages <= 1) return null;
  
  return (
    <div className="flex flex-col sm:flex-row justify-between items-center mt-6 gap-4">
      <div className="flex items-center gap-2">
        <span className="text-sm text-gray-600">Mostrar:</span>
        <select
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          className="border rounded px-2 py-1 text-sm focus:ring-amber-500 focus:border-amber-500"
          aria-label="Items por página"
        >
          {itemsPerPageOptions.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        <span className="text-sm text-gray-600">por página</span>
      </div>

      <div className="flex items-center gap-1">
        <button
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="px-3 py-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Anterior
        </button>

        {pageNumbers.map((page, index) => {
          if (typeof page === 'string') {
            return (
              <span key={`dots-${index}`} className="px-3 py-1 text-gray-500">
                ...
              </span>
            );
          }
          return (
            <button
              key={page}
              onClick={() => onPageChange(page)}
              className={`px-3 py-1 rounded border ${
                currentPage === page
                  ? 'bg-blue-500 text-white'
                  : 'hover:bg-gray-50'
              }`}
            >
              {page}
            </button>
          );
        })}

        <button
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="px-3 py-1 rounded border disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Siguiente
        </button>
      </div>
    </div>
  );
};

export default Pagination;