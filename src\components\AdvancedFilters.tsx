import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronDown, 
  ChevronUp, 
  X, 
  Filter, 
  Tag, 
  Search,
  Sliders
} from 'lucide-react';
import { 
  AdvancedProductFilters, 
  FilterFacet, 
  ProductPropertyType 
} from '../types/catalog';

interface AdvancedFiltersProps {
  filters: AdvancedProductFilters;
  facets: FilterFacet[];
  propertyTypes: ProductPropertyType[];
  onUpdateFilter: (key: keyof AdvancedProductFilters, value: any) => void;
  onAddTag: (tag: string) => void;
  onRemoveTag: (tag: string) => void;
  onSetProperty: (property: string, value: any) => void;
  onRemoveProperty: (property: string) => void;
  onClearFilters: () => void;
  className?: string;
}

const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  filters,
  facets,
  propertyTypes,
  onUpdateFilter,
  onAddTag,
  onRemoveTag,
  onSetProperty,
  onRemoveProperty,
  onClearFilters,
  className = ''
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['search', 'tags'])
  );
  const [newTag, setNewTag] = useState('');

  const toggleSection = (section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const handleAddTag = () => {
    if (newTag.trim()) {
      onAddTag(newTag.trim());
      setNewTag('');
    }
  };

  const handlePropertyChange = (propertyName: string, value: any, isMultiselect: boolean) => {
    if (isMultiselect) {
      const currentValues = filters.properties?.[propertyName] as string[] || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      
      if (newValues.length === 0) {
        onRemoveProperty(propertyName);
      } else {
        onSetProperty(propertyName, newValues);
      }
    } else {
      onSetProperty(propertyName, value);
    }
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.categories?.length) count += filters.categories.length;
    if (filters.industries?.length) count += filters.industries.length;
    if (filters.brands?.length) count += filters.brands.length;
    if (filters.tags?.length) count += filters.tags.length;
    if (filters.properties) count += Object.keys(filters.properties).length;
    if (filters.inStock) count++;
    return count;
  };

  const FilterSection: React.FC<{
    title: string;
    icon: React.ReactNode;
    sectionKey: string;
    children: React.ReactNode;
  }> = ({ title, icon, sectionKey, children }) => {
    const isExpanded = expandedSections.has(sectionKey);
    
    return (
      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between transition-colors"
        >
          <div className="flex items-center gap-2">
            {icon}
            <span className="font-medium text-gray-700">{title}</span>
          </div>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4 text-gray-500" />
          ) : (
            <ChevronDown className="w-4 h-4 text-gray-500" />
          )}
        </button>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="p-4 bg-white">
                {children}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header con contador de filtros activos */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Sliders className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">Filtros Avanzados</h3>
          {getActiveFiltersCount() > 0 && (
            <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
              {getActiveFiltersCount()}
            </span>
          )}
        </div>
        
        {getActiveFiltersCount() > 0 && (
          <button
            onClick={onClearFilters}
            className="text-sm text-red-600 hover:text-red-800 font-medium"
          >
            Limpiar todo
          </button>
        )}
      </div>

      {/* Búsqueda */}
      <FilterSection
        title="Búsqueda"
        icon={<Search className="w-4 h-4 text-gray-600" />}
        sectionKey="search"
      >
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            value={filters.searchTerm || ''}
            onChange={(e) => onUpdateFilter('searchTerm', e.target.value)}
            placeholder="Buscar productos..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </FilterSection>

      {/* Tags */}
      <FilterSection
        title="Tags"
        icon={<Tag className="w-4 h-4 text-gray-600" />}
        sectionKey="tags"
      >
        <div className="space-y-3">
          {/* Agregar nuevo tag */}
          <div className="flex gap-2">
            <input
              type="text"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
              placeholder="Agregar tag..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
            <button
              onClick={handleAddTag}
              disabled={!newTag.trim()}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              Agregar
            </button>
          </div>
          
          {/* Tags activos */}
          {filters.tags && filters.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {filters.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  {tag}
                  <button
                    onClick={() => onRemoveTag(tag)}
                    className="hover:text-blue-600"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </FilterSection>

      {/* Stock */}
      <FilterSection
        title="Disponibilidad"
        icon={<Filter className="w-4 h-4 text-gray-600" />}
        sectionKey="stock"
      >
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={filters.inStock || false}
            onChange={(e) => onUpdateFilter('inStock', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm text-gray-700">Solo productos en stock</span>
        </label>
      </FilterSection>

      {/* Propiedades dinámicas */}
      {facets.map((facet) => (
        <FilterSection
          key={facet.name}
          title={facet.display_name}
          icon={<Filter className="w-4 h-4 text-gray-600" />}
          sectionKey={`property_${facet.name}`}
        >
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {facet.values.map((value) => {
              const isMultiselect = facet.type === 'multiselect';
              const currentValue = filters.properties?.[facet.name];
              const isSelected = isMultiselect 
                ? Array.isArray(currentValue) && currentValue.includes(String(value.value))
                : currentValue === value.value;

              return (
                <label key={String(value.value)} className="flex items-center gap-2 cursor-pointer">
                  <input
                    type={isMultiselect ? 'checkbox' : 'radio'}
                    name={facet.name}
                    checked={isSelected}
                    onChange={() => handlePropertyChange(facet.name, String(value.value), isMultiselect)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 flex-1">
                    {String(value.value)}
                  </span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {value.count}
                  </span>
                </label>
              );
            })}
          </div>
        </FilterSection>
      ))}
    </div>
  );
};

export default AdvancedFilters;
