import { industriesPPE } from '../config/ppeData';
import { ppeCategories } from '../config/ppeCategories';
import { supabase as supabaseClientUntyped } from '../lib/supabase';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database.types';

const supabase: SupabaseClient<Database> = supabaseClientUntyped as SupabaseClient<Database>;

export const getAllPpeVisualizerImageUrls = (): string[] => {
  return industriesPPE.map(industry => {
    const { data } = supabase.storage
      .from('static-images') // Asegúrate que este sea el nombre correcto de tu bucket
      .getPublicUrl(`industry-selector/body-${industry.name.toLowerCase()}.jpg`);
    return data?.publicUrl || '/images/placeholder-product.jpg'; // Fallback por si alguna URL no se genera
  }).filter(url => url !== '/images/placeholder-product.jpg'); // Opcional: no precargar el placeholder
};

// Función para obtener todas las URLs de imágenes de categorías EPP
export const getAllEPPCategoryImageUrls = (): string[] => {
  const SUPABASE_CATEGORY_IMAGE_BASE_URL = `${supabase.storageUrl}/object/public/static-images/industry-selector/`;

  return ppeCategories.map(category => {
    return `${SUPABASE_CATEGORY_IMAGE_BASE_URL}body-${category.id}.jpg`;
  });
};

// Función para precargar una lista de imágenes
export const preloadImages = (urls: string[]): void => {
  console.log(`Preloading ${urls.length} images in background...`);
  urls.forEach(url => {
    const img = new Image();
    img.src = url;
    // Opcional: puedes añadir manejadores onload/onerror para logging detallado
    // img.onload = () => console.log(`Successfully preloaded: ${url}`);
    // img.onerror = () => console.warn(`Failed to preload: ${url}`);
  });
};

// Función para precargar imágenes con promesas para mejor control
export const preloadImagesWithPromises = (urls: string[]): Promise<void[]> => {
  console.log(`Preloading ${urls.length} images with promises...`);

  const preloadPromises = urls.map(url => {
    return new Promise<void>((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        console.log(`Successfully preloaded: ${url}`);
        resolve();
      };

      img.onerror = () => {
        console.warn(`Failed to preload: ${url}`);
        resolve(); // Resolvemos incluso en error para no bloquear otras imágenes
      };

      img.src = url;
    });
  });

  return Promise.all(preloadPromises);
};