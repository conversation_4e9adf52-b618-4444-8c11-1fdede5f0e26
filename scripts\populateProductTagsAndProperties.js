import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Faltan variables de entorno VITE_SUPABASE_URL o VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Mapeo de categorías a tags y propiedades comunes
const categoryTagsMap = {
  'craneana': ['seguridad', 'cabeza', 'impacto', 'construccion'],
  'ocular': ['vision', 'proteccion', 'transparente', 'laboratorio'],
  'facial': ['rostro', 'salpicaduras', 'quimica', 'soldadura'],
  'respiratoria': ['aire', 'filtros', 'particulas', 'gases'],
  'indumentaria': ['ropa', 'trabajo', 'comodidad', 'durabilidad'],
  'manos': ['guantes', 'agarre', 'destreza', 'proteccion'],
  'calzado': ['pies', 'antideslizante', 'puntera', 'comodidad'],
  'auditiva': ['ruido', 'oidos', 'concentracion', 'confort'],
  'contra-fuego': ['ignifugo', 'calor', 'llamas', 'emergencia'],
  'altura': ['arnes', 'caidas', 'seguridad', 'rescate']
};

// Propiedades comunes por categoría
const categoryPropertiesMap = {
  'craneana': {
    normativa: ['ANSI Z89.1', 'EN 397'],
    material: ['ABS', 'Polietileno'],
    resistencia_impacto: 'Alta',
    color: ['Blanco', 'Amarillo'],
    certificacion: ['CE', 'ANSI'],
    uso_recomendado: ['Construcción', 'Industria']
  },
  'ocular': {
    normativa: ['ANSI Z87.1', 'EN 166'],
    material: ['Policarbonato'],
    resistencia_impacto: 'Media',
    color: ['Transparente'],
    certificacion: ['CE', 'ANSI'],
    uso_recomendado: ['Laboratorio', 'Industria']
  },
  'manos': {
    normativa: ['EN 388', 'EN 407'],
    material: ['Nitrilo', 'Cuero'],
    resistencia_corte: 'Nivel 3',
    talla: ['M', 'L', 'XL'],
    certificacion: ['CE'],
    uso_recomendado: ['Construcción', 'Química']
  },
  'indumentaria': {
    material: ['Algodón', 'Poliéster'],
    talla: ['M', 'L', 'XL'],
    color: ['Azul', 'Gris'],
    uso_recomendado: ['Industria', 'Construcción']
  }
};

// Marcas y sus tags específicos
const brandTagsMap = {
  '3M': ['innovacion', 'calidad', 'tecnologia', 'confiable'],
  'MSA': ['profesional', 'resistente', 'certificado'],
  'HONEYWELL': ['precision', 'durabilidad', 'ergonomico'],
  'ANSELL': ['especializado', 'quimica', 'laboratorio'],
  'UVEX': ['vision', 'comodidad', 'deportivo']
};

async function populateProductTagsAndProperties() {
  console.log('🚀 Iniciando población de tags y propiedades...');

  try {
    // 1. Obtener todos los productos
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('*');

    if (fetchError) {
      throw fetchError;
    }

    console.log(`📦 Encontrados ${products.length} productos`);

    // 2. Procesar cada producto
    let updatedCount = 0;
    let errorCount = 0;

    for (const product of products) {
      try {
        const tags = new Set();
        const properties = {};

        // Agregar tags basados en categorías
        if (product.categories && Array.isArray(product.categories)) {
          product.categories.forEach(category => {
            const categoryTags = categoryTagsMap[category];
            if (categoryTags) {
              categoryTags.forEach(tag => tags.add(tag));
            }

            // Agregar propiedades basadas en categoría
            const categoryProps = categoryPropertiesMap[category];
            if (categoryProps) {
              Object.assign(properties, categoryProps);
            }
          });
        }

        // Agregar tags basados en marca
        if (product.brand) {
          const brandTags = brandTagsMap[product.brand.toUpperCase()];
          if (brandTags) {
            brandTags.forEach(tag => tags.add(tag));
          }
        }

        // Agregar tags basados en industrias
        if (product.industries && Array.isArray(product.industries)) {
          product.industries.forEach(industry => {
            tags.add(industry.toLowerCase());
          });
        }

        // Agregar tags basados en el nombre del producto
        const nameWords = product.name.toLowerCase().split(' ');
        nameWords.forEach(word => {
          if (word.length > 3 && !['para', 'con', 'sin', 'tipo'].includes(word)) {
            tags.add(word);
          }
        });

        // Agregar propiedades específicas basadas en características
        if (product.caracteristicas) {
          const caracteristicas = product.caracteristicas.toLowerCase();
          
          // Detectar normativas
          const normativas = [];
          if (caracteristicas.includes('ansi')) normativas.push('ANSI');
          if (caracteristicas.includes('en 397')) normativas.push('EN 397');
          if (caracteristicas.includes('en 388')) normativas.push('EN 388');
          if (normativas.length > 0) {
            properties.normativa = normativas;
          }

          // Detectar materiales
          const materiales = [];
          if (caracteristicas.includes('abs')) materiales.push('ABS');
          if (caracteristicas.includes('policarbonato')) materiales.push('Policarbonato');
          if (caracteristicas.includes('nitrilo')) materiales.push('Nitrilo');
          if (caracteristicas.includes('cuero')) materiales.push('Cuero');
          if (materiales.length > 0) {
            properties.material = materiales;
          }

          // Detectar resistencias
          if (caracteristicas.includes('resistente al impacto')) {
            properties.resistencia_impacto = 'Alta';
          }
          if (caracteristicas.includes('resistente a químicos')) {
            properties.resistencia_quimica = true;
          }
        }

        // Agregar peso si está disponible en especificaciones
        if (product.especificaciones) {
          const pesoMatch = product.especificaciones.match(/(\d+)\s*g/i);
          if (pesoMatch) {
            properties.peso = parseInt(pesoMatch[1]);
          }
        }

        // Convertir Set a Array para tags
        const finalTags = Array.from(tags).slice(0, 10); // Limitar a 10 tags

        // Actualizar el producto
        const { error: updateError } = await supabase
          .from('products')
          .update({
            tags: finalTags,
            properties: properties
          })
          .eq('id', product.id);

        if (updateError) {
          throw updateError;
        }

        updatedCount++;
        console.log(`✅ Producto actualizado: ${product.name} (${finalTags.length} tags, ${Object.keys(properties).length} propiedades)`);

      } catch (error) {
        errorCount++;
        console.error(`❌ Error actualizando producto ${product.name}:`, error.message);
      }
    }

    console.log(`\n📊 Resumen:`);
    console.log(`   ✅ Productos actualizados: ${updatedCount}`);
    console.log(`   ❌ Errores: ${errorCount}`);
    console.log(`   📦 Total procesados: ${products.length}`);

    // 3. Verificar algunos productos actualizados
    console.log('\n🔍 Verificando productos actualizados...');
    const { data: sampleProducts } = await supabase
      .from('products')
      .select('name, tags, properties')
      .not('tags', 'is', null)
      .limit(3);

    if (sampleProducts) {
      sampleProducts.forEach(product => {
        console.log(`\n📦 ${product.name}:`);
        console.log(`   🏷️  Tags: ${product.tags?.join(', ') || 'Ninguno'}`);
        console.log(`   ⚙️  Propiedades: ${Object.keys(product.properties || {}).join(', ') || 'Ninguna'}`);
      });
    }

  } catch (error) {
    console.error('❌ Error general:', error);
    process.exit(1);
  }
}

// Ejecutar el script
populateProductTagsAndProperties()
  .then(() => {
    console.log('\n🎉 ¡Población de tags y propiedades completada!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Error fatal:', error);
    process.exit(1);
  });
