import React from 'react';
import { Tag, Plus } from 'lucide-react';
import { motion } from 'framer-motion';

interface ProductTagsProps {
  tags: string[];
  onTagClick?: (tag: string) => void;
  onAddTag?: (tag: string) => void;
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'filled';
  interactive?: boolean;
  className?: string;
}

const ProductTags: React.FC<ProductTagsProps> = ({
  tags = [],
  onTagClick,
  onAddTag,
  maxVisible = 5,
  size = 'sm',
  variant = 'default',
  interactive = true,
  className = ''
}) => {
  const [showAll, setShowAll] = React.useState(false);
  const [newTag, setNewTag] = React.useState('');
  const [isAddingTag, setIsAddingTag] = React.useState(false);

  const visibleTags = showAll ? tags : tags.slice(0, maxVisible);
  const hiddenCount = tags.length - maxVisible;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1.5',
    lg: 'text-base px-4 py-2'
  };

  const variantClasses = {
    default: 'bg-gray-100 text-gray-700 hover:bg-gray-200',
    outline: 'border border-gray-300 text-gray-700 hover:border-gray-400 hover:bg-gray-50',
    filled: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
  };

  const baseClasses = `
    inline-flex items-center gap-1 rounded-full font-medium transition-colors
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${interactive ? 'cursor-pointer' : 'cursor-default'}
  `;

  const handleTagClick = (tag: string) => {
    if (interactive && onTagClick) {
      onTagClick(tag);
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && onAddTag) {
      onAddTag(newTag.trim());
      setNewTag('');
      setIsAddingTag(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddTag();
    } else if (e.key === 'Escape') {
      setNewTag('');
      setIsAddingTag(false);
    }
  };

  if (tags.length === 0 && !onAddTag) {
    return null;
  }

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      {/* Tags existentes */}
      {visibleTags.map((tag, index) => (
        <motion.span
          key={tag}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: index * 0.05 }}
          onClick={() => handleTagClick(tag)}
          className={baseClasses}
          title={interactive ? `Filtrar por: ${tag}` : tag}
        >
          <Tag className="w-3 h-3" />
          {tag}
        </motion.span>
      ))}

      {/* Mostrar más tags */}
      {!showAll && hiddenCount > 0 && (
        <button
          onClick={() => setShowAll(true)}
          className={`${baseClasses} border-dashed border-2 border-gray-300 hover:border-gray-400`}
        >
          +{hiddenCount} más
        </button>
      )}

      {/* Mostrar menos tags */}
      {showAll && hiddenCount > 0 && (
        <button
          onClick={() => setShowAll(false)}
          className={`${baseClasses} border-dashed border-2 border-gray-300 hover:border-gray-400`}
        >
          Mostrar menos
        </button>
      )}

      {/* Agregar nuevo tag */}
      {onAddTag && (
        <>
          {!isAddingTag ? (
            <button
              onClick={() => setIsAddingTag(true)}
              className={`${baseClasses} border-dashed border-2 border-blue-300 hover:border-blue-400 text-blue-600`}
              title="Agregar tag"
            >
              <Plus className="w-3 h-3" />
              Agregar tag
            </button>
          ) : (
            <div className="flex items-center gap-1">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={() => {
                  if (!newTag.trim()) {
                    setIsAddingTag(false);
                  }
                }}
                placeholder="Nuevo tag..."
                className="px-2 py-1 text-xs border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500 min-w-0 w-24"
                autoFocus
              />
              <button
                onClick={handleAddTag}
                disabled={!newTag.trim()}
                className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                ✓
              </button>
              <button
                onClick={() => {
                  setNewTag('');
                  setIsAddingTag(false);
                }}
                className="px-2 py-1 text-xs bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                ✕
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ProductTags;
