/**
 * Tipos para el sistema de propiedades de productos
 */
export type PropertyDataType = 'text' | 'number' | 'boolean' | 'select' | 'multiselect';

export interface ProductPropertyType {
  id: string;
  name: string;
  display_name: string;
  data_type: PropertyDataType;
  options: string[];
  category_ids: string[];
  is_filterable: boolean;
  is_searchable: boolean;
  sort_order: number;
}

export interface ProductProperties {
  [key: string]: string | number | boolean | string[];
}

/**
 * Representa un producto en el catálogo, adaptado de products.json.
 */
export interface CatalogProduct {
  id: string;
  name: string;
  description: string;
  price: string; // Ajustado a string para compatibilidad con ProductCard, antes number
  image_url: string; // Para Supabase y consistencia con el tipo Product original
  imageUrl: string; // Hecho requerido para compatibilidad con ProductCard

  brand: string; // Hecho requerido para compatibilidad con ProductCard
  stock: number; // Hecho requerido para compatibilidad
  icon?: string; // Campo de Supabase
  industries?: string[]; // Campo de Supabase
  categoryIds: string[]; // Nuevos IDs de categoría de catalogStructure

  // Nuevos campos para el sistema de tags y propiedades
  tags?: string[]; // Tags para filtrado y búsqueda
  properties?: ProductProperties; // Propiedades dinámicas del producto

  // Campos para compatibilidad con el tipo Product original y ProductCard
  category: string; // Categoría original o principal para visualización simple
  code: string; // Hecho requerido para compatibilidad
  normative: string; // Hecho requerido para compatibilidad
  
  // technicalSpecs sigue siendo el objeto estructurado, ahora requerido
  technicalSpecs: {
    caracteristicas?: string;
    especificaciones?: string;
    presentacion?: string;
    // Podrías añadir más campos genéricos si es necesario: [key: string]: string;
  };
  // Campos directos para compatibilidad, ahora requeridos
  características: string;
  especificaciones: string;
  presentación: string;
  documentacion: string; // Hecho requerido

  featured_status?: {
    is_featured: boolean;
    featured_type?: 'featured' | 'new' | 'top';
    end_date?: string;
  };

  // Para migración desde products.json o sistemas antiguos
  originalCategory_from_json?: string;
}

/**
 * Filtros avanzados para productos
 */
export interface AdvancedProductFilters {
  searchTerm?: string;
  categories?: string[];
  industries?: string[];
  brands?: string[];
  tags?: string[];
  properties?: ProductProperties;
  priceRange?: [number, number];
  inStock?: boolean;
}

/**
 * Resultado de búsqueda con ranking
 */
export interface ProductSearchResult extends CatalogProduct {
  rank?: number;
}

/**
 * Faceta para filtros dinámicos
 */
export interface FilterFacet {
  name: string;
  display_name: string;
  type: PropertyDataType;
  values: Array<{
    value: string | number | boolean;
    count: number;
    selected?: boolean;
  }>;
}

/**
 * Representa un nodo en el árbol de categorías del catálogo.
 * Un nodo puede ser una categoría principal o una subcategoría.
 */
export interface CatalogCategoryNode {
  id: string; // Identificador único para la categoría (ej. 'proteccion-personal', 'respiratoria')
  name: string; // Nombre para mostrar de la categoría (ej. 'Protección Personal', 'Respiratoria')
  children?: CatalogCategoryNode[]; // Subcategorías
  // Podrías añadir productIds aquí si decides que las categorías almacenen referencias a productos.
  // productIds?: string[];
}